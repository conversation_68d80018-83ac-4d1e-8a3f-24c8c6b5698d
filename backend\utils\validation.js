/**
 * Validation utilities
 */

const Joi = require('joi');
const logger = require('./logger');

/**
 * Validate environment variables
 */
const validateEnv = () => {
  const envSchema = Joi.object({
    NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
    PORT: Joi.number().default(5000),
    DATABASE_URL: Joi.string().required(),
    SUPABASE_URL: Joi.string().uri().required(),
    SUPABASE_ANON_KEY: Joi.string().required(),
    JWT_SECRET: Joi.string().min(32).required(),
    CORS_ORIGIN: Joi.string().default('http://localhost:3000'),
    MAX_FILE_SIZE: Joi.string().default('50MB'),
    UPLOAD_DIR: Joi.string().default('./uploads'),
    TEMP_DIR: Joi.string().default('./temp'),
    PYTHON_EXECUTABLE: Joi.string().default('python'),
    PROCESSING_TIMEOUT: Joi.number().default(300000),
    RATE_LIMIT_WINDOW_MS: Joi.number().default(900000),
    RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
    LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
    ENABLE_SWAGGER: Joi.boolean().default(true),
    MAX_IMAGE_WIDTH: Joi.number().default(4096),
    MAX_IMAGE_HEIGHT: Joi.number().default(4096)
  }).unknown();

  const { error, value } = envSchema.validate(process.env);

  if (error) {
    logger.error('Environment validation failed:', error.details);
    throw new Error(`Environment validation failed: ${error.message}`);
  }

  // Update process.env with validated values
  Object.assign(process.env, value);
  
  logger.info('Environment validation passed');
};

/**
 * Image upload validation schema
 */
const imageUploadSchema = Joi.object({
  description: Joi.string().max(500).optional(),
  register: Joi.boolean().default(true),
  model: Joi.string().valid('default', 'gan', 'enhanced').default('default'),
  evaluate: Joi.boolean().default(true)
});

/**
 * Processing job validation schema
 */
const processingJobSchema = Joi.object({
  register: Joi.boolean().default(true),
  model: Joi.string().valid('default', 'gan', 'enhanced').default('default'),
  evaluate: Joi.boolean().default(true),
  scale_factor: Joi.number().valid(2, 4, 8).default(4)
});

/**
 * Validate file upload
 */
const validateFileUpload = (file) => {
  const errors = [];

  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/tiff'];
  if (!allowedTypes.includes(file.mimetype)) {
    errors.push('Invalid file type. Only JPEG, PNG, and TIFF are allowed.');
  }

  // Check file size (50MB limit)
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    errors.push('File size too large. Maximum size is 50MB.');
  }

  // Check filename
  if (!file.originalname || file.originalname.length > 255) {
    errors.push('Invalid filename.');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate image dimensions
 */
const validateImageDimensions = (width, height) => {
  const maxWidth = parseInt(process.env.MAX_IMAGE_WIDTH) || 4096;
  const maxHeight = parseInt(process.env.MAX_IMAGE_HEIGHT) || 4096;
  const minWidth = 64;
  const minHeight = 64;

  const errors = [];

  if (width > maxWidth || height > maxHeight) {
    errors.push(`Image dimensions too large. Maximum size is ${maxWidth}x${maxHeight} pixels.`);
  }

  if (width < minWidth || height < minHeight) {
    errors.push(`Image dimensions too small. Minimum size is ${minWidth}x${minHeight} pixels.`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize filename
 */
const sanitizeFilename = (filename) => {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .substring(0, 255);
};

/**
 * Validate UUID
 */
const isValidUUID = (uuid) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validate processing parameters
 */
const validateProcessingParams = (params) => {
  const { error, value } = processingJobSchema.validate(params);
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => detail.message),
      value: null
    };
  }

  return {
    isValid: true,
    errors: [],
    value
  };
};

/**
 * Validate pagination parameters
 */
const validatePagination = (query) => {
  const schema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sortBy: Joi.string().valid('created_at', 'updated_at', 'status').default('created_at'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  });

  const { error, value } = schema.validate(query);
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => detail.message),
      value: null
    };
  }

  return {
    isValid: true,
    errors: [],
    value
  };
};

module.exports = {
  validateEnv,
  imageUploadSchema,
  processingJobSchema,
  validateFileUpload,
  validateImageDimensions,
  sanitizeFilename,
  isValidUUID,
  validateProcessingParams,
  validatePagination
};
