/**
 * Database configuration for Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');

// Validate required environment variables
if (!process.env.SUPABASE_URL) {
  throw new Error('SUPABASE_URL environment variable is required');
}

if (!process.env.SUPABASE_ANON_KEY) {
  throw new Error('SUPABASE_ANON_KEY environment variable is required');
}

// Create Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: false
    }
  }
);

/**
 * Test database connection
 */
const testConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('processing_jobs')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      logger.error('Database connection test failed:', error);
      return false;
    }
    
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection error:', error);
    return false;
  }
};

/**
 * Initialize database tables if they don't exist
 */
const initializeTables = async () => {
  try {
    // Check if tables exist by trying to query them
    const tables = [
      'processing_jobs',
      'evaluation_results',
      'user_sessions'
    ];

    for (const table of tables) {
      const { error } = await supabase
        .from(table)
        .select('count', { count: 'exact', head: true });
      
      if (error && error.code === 'PGRST116') {
        logger.warn(`Table ${table} does not exist. Please create it manually.`);
      }
    }

    logger.info('Database tables check completed');
  } catch (error) {
    logger.error('Error checking database tables:', error);
  }
};

/**
 * Database schema definitions for reference
 * These should be created manually in Supabase
 */
const schemas = {
  processing_jobs: `
    CREATE TABLE IF NOT EXISTS processing_jobs (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      status VARCHAR(50) NOT NULL DEFAULT 'uploaded',
      image1_path TEXT NOT NULL,
      image2_path TEXT NOT NULL,
      image1_filename VARCHAR(255) NOT NULL,
      image2_filename VARCHAR(255) NOT NULL,
      output_path TEXT,
      description TEXT,
      result JSONB,
      error_message TEXT,
      processing_params JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_processing_jobs_status ON processing_jobs(status);
    CREATE INDEX IF NOT EXISTS idx_processing_jobs_created_at ON processing_jobs(created_at);
  `,
  
  evaluation_results: `
    CREATE TABLE IF NOT EXISTS evaluation_results (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      job_id UUID REFERENCES processing_jobs(id) ON DELETE CASCADE,
      image_path TEXT NOT NULL,
      reference_path TEXT,
      metrics JSONB NOT NULL,
      blind_metrics JSONB,
      satellite_metrics JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_evaluation_results_job_id ON evaluation_results(job_id);
  `,
  
  user_sessions: `
    CREATE TABLE IF NOT EXISTS user_sessions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      session_token VARCHAR(255) UNIQUE NOT NULL,
      user_id VARCHAR(255),
      ip_address INET,
      user_agent TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      expires_at TIMESTAMP WITH TIME ZONE NOT NULL
    );
    
    CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
    CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
  `
};

/**
 * Helper functions for common database operations
 */
const dbHelpers = {
  /**
   * Create a new processing job
   */
  createProcessingJob: async (jobData) => {
    const { data, error } = await supabase
      .from('processing_jobs')
      .insert([{
        ...jobData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();
    
    if (error) {
      logger.error('Error creating processing job:', error);
      throw error;
    }
    
    return data;
  },

  /**
   * Update processing job status
   */
  updateJobStatus: async (jobId, status, additionalData = {}) => {
    const { data, error } = await supabase
      .from('processing_jobs')
      .update({
        status,
        ...additionalData,
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId)
      .select()
      .single();
    
    if (error) {
      logger.error('Error updating job status:', error);
      throw error;
    }
    
    return data;
  },

  /**
   * Get processing job by ID
   */
  getProcessingJob: async (jobId) => {
    const { data, error } = await supabase
      .from('processing_jobs')
      .select('*')
      .eq('id', jobId)
      .single();
    
    if (error) {
      logger.error('Error getting processing job:', error);
      throw error;
    }
    
    return data;
  },

  /**
   * Get processing jobs with pagination
   */
  getProcessingJobs: async (page = 1, limit = 10, filters = {}) => {
    let query = supabase
      .from('processing_jobs')
      .select('*', { count: 'exact' });
    
    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    
    if (filters.dateFrom) {
      query = query.gte('created_at', filters.dateFrom);
    }
    
    if (filters.dateTo) {
      query = query.lte('created_at', filters.dateTo);
    }
    
    // Apply pagination
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    const { data, error, count } = await query;
    
    if (error) {
      logger.error('Error getting processing jobs:', error);
      throw error;
    }
    
    return {
      data,
      count,
      page,
      limit,
      totalPages: Math.ceil(count / limit)
    };
  },

  /**
   * Save evaluation results
   */
  saveEvaluationResults: async (evaluationData) => {
    const { data, error } = await supabase
      .from('evaluation_results')
      .insert([{
        ...evaluationData,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();
    
    if (error) {
      logger.error('Error saving evaluation results:', error);
      throw error;
    }
    
    return data;
  }
};

module.exports = {
  supabase,
  testConnection,
  initializeTables,
  schemas,
  dbHelpers
};
