{"name": "dual-image-sr-frontend", "version": "1.0.0", "description": "Frontend for Dual Image Super Resolution with Blind Evaluation", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-hook-form": "^7.45.4", "framer-motion": "^10.16.1", "lucide-react": "^0.263.1", "recharts": "^2.8.0", "react-image-gallery": "^1.3.0", "react-compare-image": "^2.0.4", "react-loading-skeleton": "^3.3.1", "react-intersection-observer": "^9.5.2", "react-helmet-async": "^1.3.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "date-fns": "^2.30.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.1"}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.28", "eslint": "^8.47.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.2", "prettier-plugin-tailwindcss": "^0.5.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}