/**
 * Processing Routes for Dual Image Super Resolution
 * Handles image upload, processing, and result management
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { spawn } = require('child_process');
const sharp = require('sharp');
const { body, validationResult } = require('express-validator');

const logger = require('../utils/logger');
const { supabase } = require('../config/database');
const auth = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/tiff'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and TIFF are allowed.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 2
  }
});

/**
 * @swagger
 * /api/processing/upload:
 *   post:
 *     summary: Upload dual images for processing
 *     tags: [Processing]
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: image1
 *         type: file
 *         required: true
 *         description: First input image
 *       - in: formData
 *         name: image2
 *         type: file
 *         required: true
 *         description: Second input image
 *       - in: formData
 *         name: description
 *         type: string
 *         description: Processing description
 *     responses:
 *       200:
 *         description: Images uploaded successfully
 *       400:
 *         description: Invalid input
 *       500:
 *         description: Server error
 */
router.post('/upload', upload.fields([
  { name: 'image1', maxCount: 1 },
  { name: 'image2', maxCount: 1 }
]), async (req, res) => {
  try {
    const { files, body } = req;
    
    if (!files.image1 || !files.image2) {
      return res.status(400).json({
        error: 'Both images are required',
        message: 'Please upload both image1 and image2'
      });
    }

    const image1 = files.image1[0];
    const image2 = files.image2[0];

    // Validate image dimensions and format
    const image1Info = await sharp(image1.path).metadata();
    const image2Info = await sharp(image2.path).metadata();

    if (image1Info.width > 4096 || image1Info.height > 4096 ||
        image2Info.width > 4096 || image2Info.height > 4096) {
      return res.status(400).json({
        error: 'Image too large',
        message: 'Maximum image size is 4096x4096 pixels'
      });
    }

    // Create processing job record
    const jobId = uuidv4();
    const jobData = {
      id: jobId,
      status: 'uploaded',
      image1_path: image1.path,
      image2_path: image2.path,
      image1_filename: image1.originalname,
      image2_filename: image2.originalname,
      description: body.description || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Store in database
    const { data, error } = await supabase
      .from('processing_jobs')
      .insert([jobData])
      .select();

    if (error) {
      logger.error('Database error:', error);
      return res.status(500).json({
        error: 'Database error',
        message: 'Failed to create processing job'
      });
    }

    res.status(200).json({
      success: true,
      jobId,
      message: 'Images uploaded successfully',
      data: {
        job: data[0],
        image1: {
          filename: image1.originalname,
          size: image1.size,
          dimensions: `${image1Info.width}x${image1Info.height}`
        },
        image2: {
          filename: image2.originalname,
          size: image2.size,
          dimensions: `${image2Info.width}x${image2Info.height}`
        }
      }
    });

  } catch (error) {
    logger.error('Upload error:', error);
    res.status(500).json({
      error: 'Upload failed',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/processing/process/{jobId}:
 *   post:
 *     summary: Start processing uploaded images
 *     tags: [Processing]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *         description: Processing job ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               register:
 *                 type: boolean
 *                 default: true
 *               model:
 *                 type: string
 *                 default: "default"
 *               evaluate:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Processing started
 *       404:
 *         description: Job not found
 *       500:
 *         description: Processing failed
 */
router.post('/process/:jobId', [
  body('register').optional().isBoolean(),
  body('model').optional().isString(),
  body('evaluate').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation error',
        details: errors.array()
      });
    }

    const { jobId } = req.params;
    const { register = true, model = 'default', evaluate = true } = req.body;

    // Get job from database
    const { data: job, error } = await supabase
      .from('processing_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (error || !job) {
      return res.status(404).json({
        error: 'Job not found',
        message: 'Processing job does not exist'
      });
    }

    if (job.status !== 'uploaded') {
      return res.status(400).json({
        error: 'Invalid job status',
        message: `Job is in ${job.status} status, cannot process`
      });
    }

    // Update job status to processing
    await supabase
      .from('processing_jobs')
      .update({
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);

    // Prepare output path
    const resultsDir = path.join(__dirname, '../results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    const outputPath = path.join(resultsDir, `${jobId}_result.png`);

    // Prepare Python processing command
    const pythonScript = path.join(__dirname, '../../python_backend/image_processor.py');
    const args = [
      pythonScript,
      '--img1', job.image1_path,
      '--img2', job.image2_path,
      '--output', outputPath
    ];

    if (register) args.push('--register');
    if (evaluate) args.push('--evaluate');
    if (model !== 'default') args.push('--model', model);

    // Start processing asynchronously
    const pythonProcess = spawn('python', args);
    
    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    pythonProcess.on('close', async (code) => {
      try {
        let result;
        let status;
        let errorMessage = null;

        if (code === 0) {
          // Processing successful
          try {
            result = JSON.parse(stdout);
            status = 'completed';
          } catch (parseError) {
            result = { success: true, output_path: outputPath };
            status = 'completed';
          }
        } else {
          // Processing failed
          status = 'failed';
          errorMessage = stderr || 'Processing failed with unknown error';
          result = { success: false, error: errorMessage };
        }

        // Update job in database
        const updateData = {
          status,
          result: JSON.stringify(result),
          output_path: code === 0 ? outputPath : null,
          error_message: errorMessage,
          updated_at: new Date().toISOString()
        };

        await supabase
          .from('processing_jobs')
          .update(updateData)
          .eq('id', jobId);

        logger.info(`Job ${jobId} completed with status: ${status}`);

      } catch (updateError) {
        logger.error('Error updating job status:', updateError);
      }
    });

    res.status(200).json({
      success: true,
      message: 'Processing started',
      jobId,
      status: 'processing'
    });

  } catch (error) {
    logger.error('Processing error:', error);
    
    // Update job status to failed
    try {
      await supabase
        .from('processing_jobs')
        .update({
          status: 'failed',
          error_message: error.message,
          updated_at: new Date().toISOString()
        })
        .eq('id', req.params.jobId);
    } catch (updateError) {
      logger.error('Error updating job status:', updateError);
    }

    res.status(500).json({
      error: 'Processing failed',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/processing/status/{jobId}:
 *   get:
 *     summary: Get processing job status
 *     tags: [Processing]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *         description: Processing job ID
 *     responses:
 *       200:
 *         description: Job status retrieved
 *       404:
 *         description: Job not found
 */
router.get('/status/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;

    const { data: job, error } = await supabase
      .from('processing_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (error || !job) {
      return res.status(404).json({
        error: 'Job not found',
        message: 'Processing job does not exist'
      });
    }

    // Parse result if available
    let result = null;
    if (job.result) {
      try {
        result = JSON.parse(job.result);
      } catch (parseError) {
        logger.error('Error parsing job result:', parseError);
      }
    }

    res.status(200).json({
      success: true,
      job: {
        id: job.id,
        status: job.status,
        description: job.description,
        created_at: job.created_at,
        updated_at: job.updated_at,
        result,
        error_message: job.error_message,
        output_available: !!job.output_path && fs.existsSync(job.output_path)
      }
    });

  } catch (error) {
    logger.error('Status check error:', error);
    res.status(500).json({
      error: 'Status check failed',
      message: error.message
    });
  }
});

module.exports = router;
