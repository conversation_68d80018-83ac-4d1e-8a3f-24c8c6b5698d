"""
Image Processing Backend for Dual Image Super Resolution
Handles image preprocessing, model inference, and quality assessment
"""

import os
import sys
import json
import argparse
import numpy as np
import cv2
from PIL import Image
import torch
import torch.nn.functional as F
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional, Any
import time
from datetime import datetime

# Add models directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'models'))
sys.path.append(os.path.dirname(__file__))

from dual_image_sr import create_dual_sr_model, initialize_weights
from blind_evaluation import BlindQualityAssessment, SatelliteSpecificMetrics
from sklearn.metrics import mean_squared_error
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImageRegistration:
    """Image registration for dual image alignment"""
    
    def __init__(self):
        self.orb = cv2.ORB_create(nfeatures=5000)
        self.matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    
    def register_images(self, img1: np.ndarray, img2: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Register two images using ORB features"""
        try:
            # Convert to grayscale for feature detection
            gray1 = cv2.cvtColor(img1, cv2.COLOR_RGB2GRAY) if len(img1.shape) == 3 else img1
            gray2 = cv2.cvtColor(img2, cv2.COLOR_RGB2GRAY) if len(img2.shape) == 3 else img2
            
            # Detect keypoints and descriptors
            kp1, des1 = self.orb.detectAndCompute(gray1, None)
            kp2, des2 = self.orb.detectAndCompute(gray2, None)
            
            if des1 is None or des2 is None:
                logger.warning("No features detected, returning original images")
                return img1, img2
            
            # Match features
            matches = self.matcher.match(des1, des2)
            matches = sorted(matches, key=lambda x: x.distance)
            
            if len(matches) < 10:
                logger.warning("Insufficient matches for registration")
                return img1, img2
            
            # Extract matched points
            src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
            
            # Find homography
            H, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
            
            if H is None:
                logger.warning("Could not compute homography")
                return img1, img2
            
            # Warp image
            h, w = img2.shape[:2]
            img1_registered = cv2.warpPerspective(img1, H, (w, h))
            
            return img1_registered, img2
            
        except Exception as e:
            logger.error(f"Registration failed: {e}")
            return img1, img2

class DualImageProcessor:
    """Main processor for dual image super resolution"""
    
    def __init__(self, model_path: Optional[str] = None, device: str = 'auto'):
        self.device = self._setup_device(device)
        self.model = None
        self.registration = ImageRegistration()
        self.quality_assessor = BlindQualityAssessment(self.device)
        self.satellite_metrics = SatelliteSpecificMetrics()
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            self._create_default_model()
    
    def _setup_device(self, device: str) -> torch.device:
        """Setup computation device"""
        if device == 'auto':
            return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.device(device)
    
    def _create_default_model(self):
        """Create default model"""
        try:
            self.model = create_dual_sr_model(
                'basic', 
                in_channels=3, 
                out_channels=3, 
                scale_factor=4
            )
            initialize_weights(self.model)
            self.model = self.model.to(self.device)
            self.model.eval()
            logger.info("Created default model")
        except Exception as e:
            logger.error(f"Failed to create model: {e}")
            raise
    
    def load_model(self, model_path: str):
        """Load trained model"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Create model
            model_config = checkpoint.get('config', {})
            self.model = create_dual_sr_model(**model_config)
            
            # Load weights
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model = self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"Loaded model from {model_path}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            self._create_default_model()
    
    def preprocess_image(self, image_path: str, target_size: Tuple[int, int] = (256, 256)) -> np.ndarray:
        """Preprocess single image"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Resize if needed
            if target_size:
                image = cv2.resize(image, target_size, interpolation=cv2.INTER_CUBIC)
            
            # Normalize to [0, 1]
            image = image.astype(np.float32) / 255.0
            
            return image
            
        except Exception as e:
            logger.error(f"Preprocessing failed for {image_path}: {e}")
            raise
    
    def preprocess_dual_images(self, 
                             img1_path: str, 
                             img2_path: str,
                             register: bool = True,
                             target_size: Tuple[int, int] = (256, 256)) -> Tuple[torch.Tensor, torch.Tensor]:
        """Preprocess dual images for model input"""
        
        # Load and preprocess images
        img1 = self.preprocess_image(img1_path, target_size)
        img2 = self.preprocess_image(img2_path, target_size)
        
        # Register images if requested
        if register:
            img1, img2 = self.registration.register_images(
                (img1 * 255).astype(np.uint8),
                (img2 * 255).astype(np.uint8)
            )
            img1 = img1.astype(np.float32) / 255.0
            img2 = img2.astype(np.float32) / 255.0
        
        # Convert to tensors
        img1_tensor = torch.from_numpy(img1.transpose(2, 0, 1)).unsqueeze(0).to(self.device)
        img2_tensor = torch.from_numpy(img2.transpose(2, 0, 1)).unsqueeze(0).to(self.device)
        
        return img1_tensor, img2_tensor
    
    def super_resolve(self, 
                     img1_path: str, 
                     img2_path: str,
                     output_path: str,
                     register: bool = True) -> Dict[str, Any]:
        """Perform dual image super resolution"""
        
        start_time = time.time()
        
        try:
            # Preprocess images
            img1_tensor, img2_tensor = self.preprocess_dual_images(
                img1_path, img2_path, register
            )
            
            # Model inference
            with torch.no_grad():
                sr_output = self.model(img1_tensor, img2_tensor)
            
            # Convert output to numpy
            sr_image = sr_output.squeeze(0).cpu().numpy().transpose(1, 2, 0)
            sr_image = np.clip(sr_image * 255, 0, 255).astype(np.uint8)
            
            # Save output
            cv2.imwrite(output_path, cv2.cvtColor(sr_image, cv2.COLOR_RGB2BGR))
            
            processing_time = time.time() - start_time
            
            return {
                'success': True,
                'output_path': output_path,
                'processing_time': processing_time,
                'output_shape': sr_image.shape,
                'message': 'Super resolution completed successfully'
            }
            
        except Exception as e:
            logger.error(f"Super resolution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def evaluate_quality(self, 
                        image_path: str,
                        reference_path: Optional[str] = None) -> Dict[str, float]:
        """Evaluate image quality using blind and reference metrics"""
        
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Blind quality assessment
            blind_metrics = self.quality_assessor.comprehensive_assessment(image_rgb)
            
            # Satellite-specific metrics
            blind_metrics['cloud_coverage'] = self.satellite_metrics.calculate_cloud_coverage(image_rgb)
            blind_metrics['shadow_ratio'] = self.satellite_metrics.calculate_shadow_ratio(image_rgb)
            blind_metrics['texture_richness'] = self.satellite_metrics.calculate_texture_richness(image_rgb)
            
            # Reference metrics if reference image is provided
            if reference_path and os.path.exists(reference_path):
                ref_image = cv2.imread(reference_path)
                ref_image_rgb = cv2.cvtColor(ref_image, cv2.COLOR_BGR2RGB)
                
                # Resize to same dimensions if needed
                if image_rgb.shape != ref_image_rgb.shape:
                    ref_image_rgb = cv2.resize(ref_image_rgb, 
                                             (image_rgb.shape[1], image_rgb.shape[0]))
                
                # Calculate reference metrics
                blind_metrics['mse'] = mean_squared_error(
                    ref_image_rgb.flatten(), image_rgb.flatten()
                )
                blind_metrics['rmse'] = np.sqrt(blind_metrics['mse'])
                blind_metrics['psnr_ref'] = psnr(ref_image_rgb, image_rgb)
                blind_metrics['ssim_ref'] = ssim(
                    ref_image_rgb, image_rgb, multichannel=True, channel_axis=2
                )
            
            return blind_metrics
            
        except Exception as e:
            logger.error(f"Quality evaluation failed: {e}")
            return {'error': str(e)}
    
    def process_batch(self, 
                     input_pairs: List[Tuple[str, str]], 
                     output_dir: str,
                     evaluate: bool = True) -> List[Dict[str, Any]]:
        """Process multiple image pairs"""
        
        results = []
        os.makedirs(output_dir, exist_ok=True)
        
        for i, (img1_path, img2_path) in enumerate(input_pairs):
            try:
                # Generate output path
                output_filename = f"sr_result_{i:04d}.png"
                output_path = os.path.join(output_dir, output_filename)
                
                # Super resolve
                sr_result = self.super_resolve(img1_path, img2_path, output_path)
                
                # Evaluate quality if requested
                if evaluate and sr_result['success']:
                    quality_metrics = self.evaluate_quality(output_path)
                    sr_result['quality_metrics'] = quality_metrics
                
                sr_result['input_pair'] = (img1_path, img2_path)
                results.append(sr_result)
                
                logger.info(f"Processed pair {i+1}/{len(input_pairs)}")
                
            except Exception as e:
                logger.error(f"Failed to process pair {i}: {e}")
                results.append({
                    'success': False,
                    'error': str(e),
                    'input_pair': (img1_path, img2_path)
                })
        
        return results

def main():
    """Main function for command line interface"""
    parser = argparse.ArgumentParser(description='Dual Image Super Resolution Processor')
    parser.add_argument('--img1', required=True, help='Path to first input image')
    parser.add_argument('--img2', required=True, help='Path to second input image')
    parser.add_argument('--output', required=True, help='Path to output image')
    parser.add_argument('--model', help='Path to trained model')
    parser.add_argument('--register', action='store_true', help='Register images before processing')
    parser.add_argument('--evaluate', action='store_true', help='Evaluate output quality')
    parser.add_argument('--reference', help='Reference image for evaluation')
    parser.add_argument('--device', default='auto', help='Device to use (auto, cpu, cuda)')
    
    args = parser.parse_args()
    
    # Create processor
    processor = DualImageProcessor(args.model, args.device)
    
    # Process images
    result = processor.super_resolve(
        args.img1, 
        args.img2, 
        args.output, 
        args.register
    )
    
    # Evaluate if requested
    if args.evaluate and result['success']:
        quality_metrics = processor.evaluate_quality(args.output, args.reference)
        result['quality_metrics'] = quality_metrics
    
    # Output results
    print(json.dumps(result, indent=2))
    
    return 0 if result['success'] else 1

if __name__ == "__main__":
    sys.exit(main())
