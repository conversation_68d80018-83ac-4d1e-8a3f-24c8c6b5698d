"""
Blind Evaluation Framework for Satellite Image Super Resolution
No-reference quality assessment metrics for satellite imagery
"""

import numpy as np
import cv2
from PIL import Image
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy import ndimage
from scipy.stats import entropy
from skimage import feature, filters, measure
from skimage.restoration import estimate_sigma
import math
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class BlindQualityAssessment:
    """
    Comprehensive blind quality assessment for satellite imagery
    Implements multiple no-reference metrics
    """
    
    def __init__(self, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        
    def calculate_brisque(self, image: np.ndarray) -> float:
        """
        Blind/Referenceless Image Spatial Quality Evaluator (BRISQUE)
        Lower values indicate better quality
        """
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        image = image.astype(np.float64)
        
        # Mean subtraction normalization
        mu = cv2.GaussianBlur(image, (7, 7), 1.166)
        mu_sq = mu * mu
        sigma = cv2.GaussianBlur(image * image, (7, 7), 1.166)
        sigma = np.sqrt(np.abs(sigma - mu_sq))
        
        # Avoid division by zero
        sigma[sigma == 0] = 1
        structdis = (image - mu) / sigma
        
        # Calculate features
        indices = np.arange(1, structdis.size + 1)
        
        # MSCN coefficients
        alpha, beta_l, beta_r = self._estimate_aggd_params(structdis.flatten())
        
        # Pairwise products
        shifts = [(0, 1), (1, 0), (1, 1), (-1, 1)]
        pairwise_features = []
        
        for shift in shifts:
            shifted = np.roll(np.roll(structdis, shift[0], axis=0), shift[1], axis=1)
            pairwise = structdis * shifted
            alpha_pair, beta_l_pair, beta_r_pair = self._estimate_aggd_params(pairwise.flatten())
            pairwise_features.extend([alpha_pair, (beta_l_pair + beta_r_pair) / 2])
        
        features = [alpha, (beta_l + beta_r) / 2] + pairwise_features
        
        # Simple quality score (lower is better)
        return np.mean(np.abs(features))
    
    def _estimate_aggd_params(self, vec: np.ndarray) -> Tuple[float, float, float]:
        """Estimate Asymmetric Generalized Gaussian Distribution parameters"""
        vec = vec[~np.isnan(vec)]
        if len(vec) == 0:
            return 1.0, 1.0, 1.0
            
        # Estimate parameters using method of moments
        mean_val = np.mean(vec)
        var_val = np.var(vec)
        
        # Left and right variances
        left_vec = vec[vec < mean_val]
        right_vec = vec[vec >= mean_val]
        
        if len(left_vec) == 0 or len(right_vec) == 0:
            return 1.0, 1.0, 1.0
        
        beta_l = np.sqrt(np.var(left_vec))
        beta_r = np.sqrt(np.var(right_vec))
        
        # Shape parameter (simplified estimation)
        alpha = max(0.1, min(10.0, 2.0 / (1 + var_val)))
        
        return alpha, max(0.1, beta_l), max(0.1, beta_r)
    
    def calculate_niqe(self, image: np.ndarray) -> float:
        """
        Natural Image Quality Evaluator (NIQE)
        Lower values indicate better quality
        """
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        image = image.astype(np.float64)
        
        # Patch-based features
        patch_size = 96
        patches = []
        
        for i in range(0, image.shape[0] - patch_size + 1, patch_size // 2):
            for j in range(0, image.shape[1] - patch_size + 1, patch_size // 2):
                patch = image[i:i+patch_size, j:j+patch_size]
                if patch.shape == (patch_size, patch_size):
                    patches.append(patch)
        
        if not patches:
            return 100.0  # High value indicates poor quality
        
        # Extract features from patches
        features = []
        for patch in patches:
            # Local mean and variance
            mu = np.mean(patch)
            sigma = np.std(patch)
            
            # Gradient magnitude
            grad_x = cv2.Sobel(patch, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(patch, cv2.CV_64F, 0, 1, ksize=3)
            grad_mag = np.sqrt(grad_x**2 + grad_y**2)
            
            # Feature vector
            patch_features = [
                mu, sigma, np.mean(grad_mag), np.std(grad_mag),
                np.percentile(patch.flatten(), 25),
                np.percentile(patch.flatten(), 75)
            ]
            features.append(patch_features)
        
        features = np.array(features)
        
        # Quality score based on feature statistics
        feature_means = np.mean(features, axis=0)
        feature_stds = np.std(features, axis=0)
        
        # Simple quality metric (lower is better)
        quality_score = np.mean(feature_stds) / (np.mean(feature_means) + 1e-8)
        
        return min(100.0, max(0.0, quality_score * 10))
    
    def calculate_sharpness_metric(self, image: np.ndarray) -> float:
        """
        Sharpness metric using Laplacian variance
        Higher values indicate better sharpness
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Laplacian variance
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        variance = laplacian.var()
        
        return variance
    
    def calculate_contrast_metric(self, image: np.ndarray) -> float:
        """
        Contrast metric using RMS contrast
        Higher values indicate better contrast
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # RMS contrast
        mean_intensity = np.mean(gray)
        rms_contrast = np.sqrt(np.mean((gray - mean_intensity) ** 2))
        
        return rms_contrast
    
    def calculate_edge_density(self, image: np.ndarray) -> float:
        """
        Edge density metric
        Higher values indicate more edge information
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Canny edge detection
        edges = cv2.Canny(gray.astype(np.uint8), 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        return edge_density
    
    def calculate_spectral_falloff(self, image: np.ndarray) -> float:
        """
        Spectral falloff metric using FFT
        Measures high-frequency content preservation
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # FFT
        f_transform = np.fft.fft2(gray)
        f_shift = np.fft.fftshift(f_transform)
        magnitude_spectrum = np.abs(f_shift)
        
        # Calculate radial average
        h, w = magnitude_spectrum.shape
        center = (h // 2, w // 2)
        
        # Create radial profile
        y, x = np.ogrid[:h, :w]
        r = np.sqrt((x - center[1])**2 + (y - center[0])**2)
        r = r.astype(int)
        
        # Radial average
        tbin = np.bincount(r.ravel(), magnitude_spectrum.ravel())
        nr = np.bincount(r.ravel())
        radial_profile = tbin / (nr + 1e-8)
        
        # Spectral falloff (slope of high frequencies)
        if len(radial_profile) > 10:
            high_freq_start = len(radial_profile) // 2
            high_freq_profile = radial_profile[high_freq_start:]
            if len(high_freq_profile) > 1:
                # Calculate slope
                x_vals = np.arange(len(high_freq_profile))
                slope = np.polyfit(x_vals, np.log(high_freq_profile + 1e-8), 1)[0]
                return abs(slope)
        
        return 0.0
    
    def calculate_noise_level(self, image: np.ndarray) -> float:
        """
        Estimate noise level in image
        Lower values indicate less noise
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Estimate noise using robust median estimator
        try:
            noise_sigma = estimate_sigma(gray, multichannel=False, average_sigmas=True)
            return noise_sigma
        except:
            # Fallback method
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            noise_estimate = np.sqrt(0.5 * np.mean(laplacian**2))
            return noise_estimate
    
    def calculate_information_content(self, image: np.ndarray) -> float:
        """
        Information content using entropy
        Higher values indicate more information
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Calculate histogram
        hist, _ = np.histogram(gray.flatten(), bins=256, range=(0, 256))
        hist = hist / hist.sum()  # Normalize
        
        # Remove zero entries
        hist = hist[hist > 0]
        
        # Calculate entropy
        info_content = entropy(hist, base=2)
        
        return info_content
    
    def comprehensive_assessment(self, image: np.ndarray) -> Dict[str, float]:
        """
        Comprehensive blind quality assessment
        Returns dictionary of all metrics
        """
        if isinstance(image, torch.Tensor):
            image = image.cpu().numpy()
        
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        
        metrics = {}
        
        try:
            metrics['brisque'] = self.calculate_brisque(image)
        except:
            metrics['brisque'] = 50.0
        
        try:
            metrics['niqe'] = self.calculate_niqe(image)
        except:
            metrics['niqe'] = 50.0
        
        try:
            metrics['sharpness'] = self.calculate_sharpness_metric(image)
        except:
            metrics['sharpness'] = 0.0
        
        try:
            metrics['contrast'] = self.calculate_contrast_metric(image)
        except:
            metrics['contrast'] = 0.0
        
        try:
            metrics['edge_density'] = self.calculate_edge_density(image)
        except:
            metrics['edge_density'] = 0.0
        
        try:
            metrics['spectral_falloff'] = self.calculate_spectral_falloff(image)
        except:
            metrics['spectral_falloff'] = 0.0
        
        try:
            metrics['noise_level'] = self.calculate_noise_level(image)
        except:
            metrics['noise_level'] = 10.0
        
        try:
            metrics['information_content'] = self.calculate_information_content(image)
        except:
            metrics['information_content'] = 5.0
        
        # Composite quality score (0-100, higher is better)
        # Normalize and combine metrics
        normalized_metrics = self._normalize_metrics(metrics)
        composite_score = self._calculate_composite_score(normalized_metrics)
        metrics['composite_score'] = composite_score
        
        return metrics
    
    def _normalize_metrics(self, metrics: Dict[str, float]) -> Dict[str, float]:
        """Normalize metrics to 0-1 range"""
        normalized = {}
        
        # For metrics where lower is better
        normalized['brisque'] = max(0, min(1, 1 - (metrics['brisque'] / 100)))
        normalized['niqe'] = max(0, min(1, 1 - (metrics['niqe'] / 100)))
        normalized['noise_level'] = max(0, min(1, 1 - (metrics['noise_level'] / 50)))
        
        # For metrics where higher is better
        normalized['sharpness'] = max(0, min(1, metrics['sharpness'] / 1000))
        normalized['contrast'] = max(0, min(1, metrics['contrast'] / 100))
        normalized['edge_density'] = max(0, min(1, metrics['edge_density']))
        normalized['spectral_falloff'] = max(0, min(1, metrics['spectral_falloff'] / 5))
        normalized['information_content'] = max(0, min(1, metrics['information_content'] / 8))
        
        return normalized
    
    def _calculate_composite_score(self, normalized_metrics: Dict[str, float]) -> float:
        """Calculate weighted composite quality score"""
        weights = {
            'brisque': 0.15,
            'niqe': 0.15,
            'sharpness': 0.20,
            'contrast': 0.15,
            'edge_density': 0.15,
            'spectral_falloff': 0.10,
            'noise_level': 0.05,
            'information_content': 0.05
        }
        
        composite = sum(normalized_metrics[metric] * weight 
                       for metric, weight in weights.items())
        
        return composite * 100  # Scale to 0-100

class SatelliteSpecificMetrics:
    """
    Satellite imagery specific quality metrics
    """
    
    def __init__(self):
        pass
    
    def calculate_cloud_coverage(self, image: np.ndarray) -> float:
        """
        Estimate cloud coverage in satellite image
        Returns percentage of cloud coverage
        """
        if len(image.shape) == 3:
            # Convert to HSV for better cloud detection
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            
            # Cloud detection based on brightness and saturation
            brightness = hsv[:, :, 2]
            saturation = hsv[:, :, 1]
            
            # Clouds are typically bright with low saturation
            cloud_mask = (brightness > 200) & (saturation < 50)
            cloud_percentage = np.sum(cloud_mask) / cloud_mask.size * 100
            
            return cloud_percentage
        
        return 0.0
    
    def calculate_shadow_ratio(self, image: np.ndarray) -> float:
        """
        Calculate shadow ratio in satellite image
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Shadow detection based on low intensity
        shadow_threshold = np.percentile(gray, 15)
        shadow_mask = gray < shadow_threshold
        shadow_ratio = np.sum(shadow_mask) / shadow_mask.size
        
        return shadow_ratio
    
    def calculate_texture_richness(self, image: np.ndarray) -> float:
        """
        Calculate texture richness using Local Binary Patterns
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Local Binary Pattern
        radius = 3
        n_points = 8 * radius
        
        try:
            lbp = feature.local_binary_pattern(gray, n_points, radius, method='uniform')
            
            # Calculate histogram of LBP
            hist, _ = np.histogram(lbp.ravel(), bins=n_points + 2, 
                                 range=(0, n_points + 2), density=True)
            
            # Texture richness as entropy of LBP histogram
            hist = hist[hist > 0]
            texture_richness = entropy(hist, base=2)
            
            return texture_richness
        except:
            return 0.0

def evaluate_image_quality(image_path: str) -> Dict[str, float]:
    """
    Convenience function to evaluate image quality
    """
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Blind quality assessment
    bqa = BlindQualityAssessment()
    quality_metrics = bqa.comprehensive_assessment(image)
    
    # Satellite-specific metrics
    sat_metrics = SatelliteSpecificMetrics()
    quality_metrics['cloud_coverage'] = sat_metrics.calculate_cloud_coverage(image)
    quality_metrics['shadow_ratio'] = sat_metrics.calculate_shadow_ratio(image)
    quality_metrics['texture_richness'] = sat_metrics.calculate_texture_richness(image)
    
    return quality_metrics

if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
        metrics = evaluate_image_quality(image_path)
        
        print("Blind Quality Assessment Results:")
        print("-" * 40)
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")
    else:
        print("Usage: python blind_evaluation.py <image_path>")
