{"name": "dual-image-sr-backend", "version": "1.0.0", "description": "Backend for Dual Image Super Resolution with Blind Evaluation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "format": "prettier --write .", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["super-resolution", "satellite-imagery", "dual-image", "blind-evaluation", "deep-learning"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "express-fileupload": "^1.4.0", "@supabase/supabase-js": "^2.33.1", "pg": "^8.11.3", "uuid": "^9.0.0", "sharp": "^0.32.4", "jimp": "^0.22.10", "node-cron": "^3.0.2", "winston": "^3.10.0", "joi": "^17.9.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-async-errors": "^3.1.1", "http-status-codes": "^2.2.0", "moment": "^2.29.4", "lodash": "^4.17.21", "axios": "^1.5.0", "form-data": "^4.0.0", "archiver": "^5.3.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.46.0", "prettier": "^3.0.1", "@types/node": "^20.4.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/dual-image-sr.git"}, "bugs": {"url": "https://github.com/yourusername/dual-image-sr/issues"}, "homepage": "https://github.com/yourusername/dual-image-sr#readme"}